import json
import os
import numpy as np
import torch
import re
import faiss                   # 替換 hnswlib
from transformers import AutoModel, AutoTokenizer
import torch.nn.functional as F
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
import uvicorn
import threading
from starlette.concurrency import run_in_threadpool

os.environ["KMP_DUPLICATE_LIB_OK"] = "TRUE"

# --- 配置 ---
EMBEDDING_MODEL_NAME = "Qwen/Qwen3-Embedding-0.6B"
DOCS_FILE = "docs.txt"
FAISS_INDEX_FILE = "faiss_hnsw_index.bin" # 替換 HNSW_INDEX_FILE
HNSW_M = 32  # FAISS 中的 HNSW M 參數
DEVICE = "cuda" if torch.cuda.is_available() else "cpu"
BATCH_SIZE = 32 # 新增：設定批次大小以避免 OOM
print("DEVICE:", DEVICE)

# --- FastAPI 應用程式實例 ---
app = FastAPI()

# --- Pydantic 模型 ---
class CommandRequest(BaseModel):
    deviceId: str
    command: str
    language: str

# --- 全域變數，用於儲存模型和索引 ---
embedding_tokenizer = None
embedding_model = None
faiss_index = None # 替換 hnsw_index
docs = None
tokenizer_lock = threading.Lock()
index_lock = threading.Lock()
CONCURRENCY_LIMIT = int(os.getenv("MAX_CONCURRENT_INFERENCES", "1" if (torch.cuda.is_available()) else str(max(1, (os.cpu_count() or 1)))))
inference_semaphore = threading.Semaphore(CONCURRENCY_LIMIT)

# --- 1. 加載模型和索引 ---
@app.on_event("startup")
def startup_event():
    global embedding_tokenizer, embedding_model, faiss_index, docs
    
    print("正在加載 Embedding 模型...")
    embedding_tokenizer = AutoTokenizer.from_pretrained(EMBEDDING_MODEL_NAME, trust_remote_code=True, use_fast=False)
    embedding_model = AutoModel.from_pretrained(EMBEDDING_MODEL_NAME, trust_remote_code=True).to(DEVICE)
    embedding_tokenizer.pad_token = embedding_tokenizer.eos_token
    embedding_model.config.pad_token_id = embedding_model.config.eos_token_id
    embedding_model.eval()
    print("模型加載完成。")

    # 設置 FAISS 與 Torch 執行緒數以提升並發檢索的穩定性
    try:
        faiss.omp_set_num_threads(os.cpu_count() or 1)
        print(f"FAISS 線程數: {os.cpu_count() or 1}")
    except Exception:
        pass

    if not os.path.exists(FAISS_INDEX_FILE) or not os.path.exists("docs_data.json"):
        print(f"{FAISS_INDEX_FILE} 或 docs_data.json 不存在，正在創建新的 FAISS 索引...")
        faiss_index, docs = create_and_save_index()
    else:
        print(f"正在從 {FAISS_INDEX_FILE} 加載 FAISS 索引...")
        with open("docs_data.json", "r", encoding='utf-8') as f:
            docs = json.load(f)
        
        index = faiss.read_index(FAISS_INDEX_FILE)
        faiss_index = index
        print("FAISS 索引加載完成。")

# --- 2. 數據處理和向量化 ---
def get_embeddings(texts):
    """
    根據 Qwen Embedding 模型的官方推薦方法，並採用更穩健的平均池化（mean pooling）策略生成句子向量。
    1. 獲取所有 token 的隱藏狀態 (last_hidden_state)。
    2. 根據 attention_mask 進行平均池化，以獲得整個句子的語義表示。
    3. 對池化後的向量進行 L2 歸一化。
    這種方法可以確保無論是單獨處理還是在批次中處理，同一個句子都能生成完全一致的向量。
    """
    with inference_semaphore:
        # 分詞器並非完全線程安全（特別是非 fast 版本），加鎖保護
        with tokenizer_lock:
            inputs = embedding_tokenizer(texts, padding=True, truncation=True, return_tensors="pt").to(DEVICE)
        # 推理採用 inference_mode 提升性能與安全性
        with torch.inference_mode():
            outputs = embedding_model(**inputs)
            last_hidden_state = outputs.last_hidden_state
            attention_mask = inputs['attention_mask']
            
            # 平均池化 (Mean Pooling)
            input_mask_expanded = attention_mask.unsqueeze(-1).expand(last_hidden_state.size()).float()
            sum_embeddings = torch.sum(last_hidden_state * input_mask_expanded, 1)
            sum_mask = torch.clamp(input_mask_expanded.sum(1), min=1e-9)
            pooled_embeddings = sum_embeddings / sum_mask
            
            # L2 歸一化
            normalized_embeddings = F.normalize(pooled_embeddings, p=2, dim=1)

    return normalized_embeddings.cpu().numpy()

def create_and_save_index():
    """加載文檔，生成 embeddings 並創建/保存 FAISS HNSW 索引"""
    print("正在加載和處理文檔...")
    with open(DOCS_FILE, 'r', encoding='utf-8') as f:
        content = f.read()

    docs_list = []
    entries = content.strip().split('\n\n')
    for entry in entries:
        # 去除每行前後空白與空行
        raw_lines = entry.strip().split('\n')
        lines = [ln.strip() for ln in raw_lines if ln.strip()]
        if not lines:
            continue

        # 從區塊尾端尋找第一個可被 JSON 解析的行，視為該區塊的輸出 JSON
        json_line_index = None
        output_json_text = None
        for idx in range(len(lines) - 1, -1, -1):
            candidate = lines[idx]
            if candidate.startswith('{') and candidate.endswith('}'):
                try:
                    # 僅用於驗證格式正確性；實際仍保存原始文本
                    json.loads(candidate)
                    json_line_index = idx
                    output_json_text = candidate
                    break
                except Exception:
                    pass

        if json_line_index is None:
            # 舊格式保障：若只有兩行但第二行非嚴格 JSON，則跳過並提示
            print("警告：跳過一個區塊，未找到有效 JSON 輸出。")
            continue

        # 語料行：為 JSON 行之前的所有行；若沒有，退回舊格式第一行語料
        utterance_lines = lines[:json_line_index]
        if not utterance_lines and len(lines) >= 2:
            utterance_lines = [lines[0]]

        # 展開為多條樣本，皆對應同一 JSON 輸出
        for utterance in utterance_lines:
            docs_list.append({
                "input": utterance,
                "output": output_json_text
            })
        
    inputs = [doc['input'] for doc in docs_list]
    
    print(f"正在為 {len(inputs)} 條文檔生成 embeddings (批次大小: {BATCH_SIZE})...")
    
    all_embeddings = []
    for i in range(0, len(inputs), BATCH_SIZE):
        batch_inputs = inputs[i:i+BATCH_SIZE]
        batch_embeddings = get_embeddings(batch_inputs)
        all_embeddings.append(batch_embeddings)
        print(f"  已處理 {min(i + BATCH_SIZE, len(inputs))} / {len(inputs)} 條文檔...")

    embeddings = np.concatenate(all_embeddings, axis=0).astype('float32') # FAISS 需要 float32
    dim = embeddings.shape[1]

    print("正在建立 FAISS HNSW 索引...")
    # 建立一個 CPU 上的 HNSW 索引，之後可以轉移到 GPU
    cpu_index = faiss.IndexHNSWFlat(dim, HNSW_M, faiss.METRIC_INNER_PRODUCT)
    cpu_index.add(embeddings)
    
    print(f"正在保存 FAISS 索引到 {FAISS_INDEX_FILE} ...")
    faiss.write_index(cpu_index, FAISS_INDEX_FILE)
    
    with open("docs_data.json", "w", encoding='utf-8') as f:
        json.dump(docs_list, f, ensure_ascii=False, indent=4)
        
    print("索引與數據保存完成。")
        
    return cpu_index, docs_list

# --- 3. 向量檢索 ---
def get_intent_directly(query: str, index, docs_data):
    """使用 FAISS-GPU 進行 ANN 檢索，返回最匹配結果與分數（內積相似度）"""
    print(f"正在為查詢 '{query}' 進行向量檢索...")
    query_embedding = get_embeddings([query]).astype('float32')
    # FAISS 的 search 方法返回距離和索引
    distances, labels = index.search(query_embedding, k=1)
    
    best_doc_index = int(labels[0][0])
    # 對於內積（INNER_PRODUCT），返回的值就是相似度
    similarity = float(distances[0][0])

    best_doc = docs_data[best_doc_index]
    print(f"最匹配的結果是: '{best_doc['input']}' (相似度分數: {similarity:.4f})")
    return best_doc['output'], similarity

def search_top_k(query: str, index, docs_data, k: int = 5):
    """
    使用 FAISS 進行 ANN 檢索，返回 top-k 候選。
    每個候選包含：rank、input、output_text、output(若能解析為 JSON)、similarity。
    """
    print(f"正在為查詢 '{query}' 進行 top-{k} 檢索...")
    query_embedding = get_embeddings([query]).astype('float32')
    with index_lock:
        distances, labels = index.search(query_embedding, k=k)
    results = []
    if labels is None or len(labels) == 0:
        return results
    for rank, label in enumerate(labels[0]):
        idx = int(label)
        if idx == -1:
            continue
        similarity = float(distances[0][rank])
        doc = docs_data[idx]
        output_text = doc['output']
        parsed_output = None
        try:
            parsed_output = json.loads(output_text)
        except json.JSONDecodeError:
            parsed_output = None
        results.append({
            "rank": rank + 1,
            "input": doc['input'],
            "output_text": output_text,
            "output": parsed_output,
            "similarity": similarity
        })
    # 日誌：打印 top-k 相似度列表
    try:
        similarity_list_str = ", ".join([f"{item['rank']}:{item['similarity']:.4f}" for item in results])
        print(f"Top-{len(results)} 候選相似度列表: {similarity_list_str}")
    except Exception:
        pass
    return results

def build_llm_fallback_prompt(user_query: str, candidates: list) -> str:
    """
    構建兜底大模型提示詞：
    - 用戶輸入
    - top-5 候選（可能有誤，僅供參考）
    - 要求大模型僅輸出最合理的 JSON
    """
    lines = []
    lines.append(f"用户输入了:{user_query}")
    lines.append("现在有top-5最相似的可能意图和槽位")
    for item in candidates[:5]:
        sim = float(item.get("similarity", 0.0))
        raw = item.get("output_text", "{}")
        lines.append(f"- 候选{item.get('rank', '?')}(相似度: {sim:.4f}):")
        lines.append(raw)
    lines.append("你可以用这几个作参考 但也有可能是错的")
    return "\n".join(lines)

# --- 4. API 端點 ---
@app.post("/process-command")
async def process_command(request: CommandRequest):
    """
    接收設備指令，透過 RAG 管道處理並返回意圖。
    成功時，直接返回意圖的 JSON 物件。
    失敗時，返回 404 錯誤。
    """
    if not all([embedding_model, embedding_tokenizer, faiss_index is not None, docs]):
        raise HTTPException(status_code=503, detail="服務尚未完全初始化，請稍後重試。")

    query = request.command
    print(f"接收到指令: {query} (來自設備: {request.deviceId}, 語言: {request.language})")

    try:
        k = min(5, len(docs)) if docs else 5
        # 將阻塞型推理與檢索放入線程池，避免阻塞事件循環
        results = await run_in_threadpool(search_top_k, query, faiss_index, docs, k)
        if not results:
            raise HTTPException(status_code=404, detail="無法識別指令：無候選結果。")

        best = results[0]
        similarity = float(best["similarity"])
        print(f"最佳候選相似度: {similarity:.4f}")

        # 若最佳相似度 >= 0.95，返回最佳意圖 JSON
        if similarity >= 0.985:
            if best["output"] is not None:
                return best["output"]
            else:
                print(f"錯誤: 檢索到的最佳文檔內容不是有效的 JSON: {best['output_text']}")
                raise HTTPException(
                    status_code=500,
                    detail="內部伺服器錯誤：檢索到的意圖格式無效。"
                )

        # 否則，構建兜底 LLM 提示詞並返回
        prompt = build_llm_fallback_prompt(query, results)
        return {"prompt": prompt}

    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        print(f"處理過程中發生未知錯誤: {e}")
        raise HTTPException(status_code=500, detail="處理指令時發生內部錯誤。")

# --- 主程序 (用於本地開發) ---
if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
