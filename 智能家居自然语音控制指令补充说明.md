# 智能家居自然语音控制指令补充说明

版本：v1.0.1

更新于：2025-07-10



## 概述

**本文档包含HTTP协议补充和意图特殊字段说明。如需对接，请查阅《Smart Hotel Control HTTP REST API 協議》。**



## HTTP协议补充

### 同步设备列表

#### 1.请求

```http
POST /api/syncDeviceList HTTP/1.1
Content-Type: application/json
```

```json
{
    "deviceId": "XXX",
    "deviceList": [
        {"name":"灯", "alias": "照明|灯光|灯具"},
        {"name":"窗帘"},
        {"name":"空调", "alias": "冷气|冷气机"},
        {"name":"风扇"},
		{"name":"卧室灯"},
        {"name":"彩色灯"},
        {"name":"按键二", "alias": "按键2"}
    ]
}
```

| 参数             | 类型         | 说明         | 备注                       |
| ---------------- | ------------ | ------------ | -------------------------- |
| deviceId         | string       | 设备唯一标识 |                            |
| deviceList       | object array | 设备列表     |                            |
| deviceList.name  | string       | 设备名称     |                            |
| deviceList.alias | string       | 设备别名     | 可省略，多个别名用“\|”分隔 |

#### 2.应答

```http
HTTP/1.1 200 OK
```

```json
{
    "sid": "XXX",
    "rc": 0
}
```

| 参数 | 类型   | 说明     | 备注          |
| ---- | ------ | -------- | ------------- |
| sid  | string | 唯一标识 |               |
| rc   | int    | 应答码   | 0表示同步成功 |



### 同步位置列表

#### 1.请求

```http
POST /api/syncPositionList HTTP/1.1
Content-Type: application/json
```

```json
{
    "deviceId": "XXX",
    "positionList": [
        {"name":"客厅"},
		{"name":"卧室"},
        {"name":"阳台"}
    ]
}
```

| 参数               | 类型         | 说明         | 备注                       |
| ------------------ | ------------ | ------------ | -------------------------- |
| deviceId           | string       | 设备唯一标识 |                            |
| positionList       | object array | 位置列表     |                            |
| positionList.name  | string       | 位置名称     |                            |
| positionList.alias | string       | 位置别名     | 可省略，多个别名用“\|”分隔 |

#### 2.应答

```http
HTTP/1.1 200 OK
```

```json
{
    "sid": "XXX",
    "rc": 0
}
```

| 参数 | 类型   | 说明     | 备注          |
| ---- | ------ | -------- | ------------- |
| sid  | string | 唯一标识 |               |
| rc   | int    | 应答码   | 0表示同步成功 |



### 同步场景列表

#### 1.请求

```http
POST /api/syncSceneList HTTP/1.1
Content-Type: application/json
```

```json
{
    "deviceId": "XXX",
    "sceneList": [
        {"name":"夏天模式", "alias": "夏日模式"},
        {"name":"冬天模式", "alias": "冬日模式"},
        {"name":"起夜模式", "alias": "夜起模式|照明模式"}
    ]
}
```

| 参数            | 类型         | 说明         | 备注                       |
| --------------- | ------------ | ------------ | -------------------------- |
| deviceId        | string       | 设备唯一标识 |                            |
| sceneList       | object array | 场景列表     |                            |
| sceneList.name  | string       | 场景名称     |                            |
| sceneList.alias | string       | 场景别名     | 可省略，多个别名用“\|”分隔 |

#### 2.应答

```http
HTTP/1.1 200 OK
```

```json
{
    "sid": "XXX",
    "rc": 0
}
```

| 参数 | 类型   | 说明     | 备注          |
| ---- | ------ | -------- | ------------- |
| sid  | string | 唯一标识 |               |
| rc   | int    | 应答码   | 0表示同步成功 |



### 解析意图

#### 1.请求

```http
POST /api/control HTTP/1.1
Content-Type: application/json
```

```json
{
    "deviceId": "XXX",
    "command": "XXX"
}
```

#### 2.应答

```http
HTTP/1.1 200 OK
```

```json
{
    "sid": "XXX",
    "text": "XXX",
    "language": "XXX",
    "rc": 0,
    "matchIntent": "XXX",
    "slotParams": {}
}
```

| 参数        | 类型   | 说明     | 备注                                                   |
| ----------- | ------ | -------- | ------------------------------------------------------ |
| sid         | string | 唯一标识 |                                                        |
| text        | string | 文本内容 |                                                        |
| language    | string | 语种     | mandarin表示普通话，cantonese表示粤语，english表示英语 |
| rc          | int    | 应答码   | 0表示命中意图，4表示没有命中意图                       |
| matchIntent | string | 匹配意图 | 可省略                                                 |
| slotParams  | object | 槽位信息 | 可省略                                                 |

下文不再详细描述请求sid、text、language、rc、matchIntent、slotParams字段的含义，只描述各个意图的特殊字段。



## 意图特殊字段说明

### switchDevice —— 转变设备

例句：打开客厅的射灯

```json
{
    "matchIntent": "switchDevice",
    "slotParams": {
        "action": "open",
        "device": "窗帘",
        "position": "射灯"
    }
}
```

| 参数                | 类型   | 说明     | 备注       |
| ------------------- | ------ | -------- | ---------- |
| slotParams.action   | string | 功能     | 见下文描述 |
| slotParams.device   | string | 设备名称 |            |
| slotParams.position | string | 位置名称 | 可省略     |

slotParams.action的值定义：

| 值    | 说明     | 例句             |
| ----- | -------- | ---------------- |
| open  | 打开设备 | 打开客厅的筒灯   |
| close | 关闭设备 | 关闭客厅的风扇   |
| pause | 暂停设备 | 把客厅的窗帘暂停 |



### sceneMode —— 场景模式

例句：执行客厅的回家模式

```json
{
    "matchIntent": "sceneMode",
    "slotParams": {
        "scene": "回家模式",
        "position": "客厅"
    }
}
```

| 参数                | 类型   | 说明     | 备注   |
| ------------------- | ------ | -------- | ------ |
| slotParams.scene    | string | 场景名称 |        |
| slotParams.position | string | 位置名称 | 可省略 |



### brightnessSet —— 亮度设置

例句：把客厅的射灯亮度调到70%

```json
{
    "matchIntent": "brightnessSet",
    "slotParams": {
        "action": "set",
        "device": "射灯",
        "position": "客厅",
        "value": 70
    }
}
```

| 参数                | 类型   | 说明     | 备注                                                         |
| ------------------- | ------ | -------- | ------------------------------------------------------------ |
| slotParams.action   | string | 功能     | 见下文描述                                                   |
| slotParams.device   | string | 设备名称 | 可省略                                                       |
| slotParams.position | string | 位置名称 | 可省略                                                       |
| slotParams.value    | int    | 亮度值   | 百分比，action为increase或reduce时可省略，<br />最亮表示100%，最暗表示0% |

slotParams.action的值定义：

| 值       | 说明     | 例句                                 |
| -------- | -------- | ------------------------------------ |
| increase | 亮度增加 | 把客厅的射灯亮度调高一点（调亮一点） |
| reduce   | 亮度减少 | 把客厅的射灯亮度调低一点（调暗一点） |
| set      | 亮度设置 | 把客厅的射灯亮度调到70%              |



### colorTemperatureSet —— 色温设置

例句：把客厅的射灯调到暖光（黄光）

```json
{
    "matchIntent": "colorTemperatureSet",
    "slotParams": {
        "action": "set",
        "device": "射灯",
        "position": "客厅",
        "value": 0
    }
}
```

| 参数                | 类型   | 说明     | 备注                                                         |
| ------------------- | ------ | -------- | ------------------------------------------------------------ |
| slotParams.action   | string | 功能     | 见下文描述                                                   |
| slotParams.device   | string | 设备名称 | 可省略                                                       |
| slotParams.position | string | 位置名称 | 可省略                                                       |
| slotParams.value    | int    | 色温值   | 百分比，action为increase或reduce时可省略，<br />冷光（白光）表示100%，暖光（黄光）表示0%，中性光表示50% |

slotParams.action的值定义：

| 值       | 说明     | 例句                           |
| -------- | -------- | ------------------------------ |
| increase | 色温增加 | 把客厅的筒灯调冷一点（白一点） |
| reduce   | 色温减少 | 把客厅的吊灯调暖一点（黄一点） |
| set      | 色温设置 | 把客厅的射灯调到暖光（黄光）   |



### rgbSet —— RGB设置

例句：把客厅的灯带调成蓝色

```json
{
    "matchIntent": "rgbSet",
    "slotParams": {
        "device": "灯带",
        "position": "客厅",
        "rgb": "#0000FF"
    }
}
```

| 参数                | 类型   | 说明     | 备注   |
| ------------------- | ------ | -------- | ------ |
| slotParams.device   | string | 设备名称 | 可省略 |
| slotParams.position | string | 位置名称 | 可省略 |
| slotParams.rgb      | string | RGB      | 16进制 |



### trackSet —— 开合度设置

例句：把客厅的窗帘打开30%

```json
{
    "matchIntent": "trackSet",
    "slotParams": {
        "action": "set",
        "device": "窗帘",
        "position": "客厅",
        "value": 30
    }
}
```

| 参数                | 类型   | 说明     | 备注                                     |
| ------------------- | ------ | -------- | ---------------------------------------- |
| slotParams.action   | string | 功能     | 见下文描述                               |
| slotParams.device   | string | 设备名称 | 可省略                                   |
| slotParams.position | string | 位置名称 | 可省略                                   |
| slotParams.value    | int    | 开合度   | 百分比，action为increase或reduce时可省略 |

slotParams.action的值定义：

| 值       | 说明       | 例句                 |
| -------- | ---------- | -------------------- |
| increase | 开合度增加 | 打开一点客厅的窗帘   |
| reduce   | 开合度减少 | 把客厅的窗帘关小一点 |
| set      | 开合度设置 | 把客厅的窗帘打开30%  |



### airConditionerSet —— 空调设置

例句：帮我把客厅的空调调成制冷模式

```json
{
    "matchIntent": "airConditionerSet",
    "slotParams": {
        "action": "cool",
        "device": "空调",
        "position": "客厅"
    }
}
```

| 参数                | 类型   | 说明     | 备注       |
| ------------------- | ------ | -------- | ---------- |
| slotParams.action   | string | 功能     | 见下文描述 |
| slotParams.device   | string | 设备名称 | 可省略     |
| slotParams.position | string | 位置名称 | 可省略     |

slotParams.action的值定义：

| 值              | 说明     | 例句                     |
| --------------- | -------- | ------------------------ |
| cool            | 制冷模式 | 把客厅的空调调成制冷模式 |
| heat            | 制热模式 | 把客厅的空调调成制热模式 |
| fan             | 送风模式 | 把客厅的空调调成送风模式 |
| dry             | 除湿模式 | 把客厅的空调调成除湿模式 |
| hi              | 强风     | 把客厅的空调调成强风     |
| med             | 中风     | 把客厅的空调调成中风     |
| lo              | 微风     | 把客厅的空调调成微风     |
| autoFan         | 自动风速 | 把客厅的空调调成自动风速 |
| verticalSwing   | 上下扫风 | 把客厅的空调调成上下扫风 |
| horizontalSwing | 左右扫风 | 把客厅的空调调成左右扫风 |
| autoSwing       | 自动风向 | 把客厅的空调调成自动风向 |
| eco             | 节能模式 | 把客厅的空调调成节能     |
| sleep           | 睡眠模式 | 把客厅的空调调成睡眠     |



### temperatureSet —— 温度设置

例句：把客厅的空调调到24度

```json
{
    "matchIntent": "temperatureSet",
    "slotParams": {
        "action": "set",
        "device": "空调",
        "position": "客厅",
        "value": 24
    }
}
```

| 参数                | 类型   | 说明     | 备注                                                         |
| ------------------- | ------ | -------- | ------------------------------------------------------------ |
| slotParams.action   | string | 功能     | 见下文描述                                                   |
| slotParams.device   | string | 设备名称 | 可省略                                                       |
| slotParams.position | string | 位置名称 | 可省略                                                       |
| slotParams.value    | int    | 温度值   | 范围1-30，action为increase或reduce时可省略<br />最热表示30度，最冷表示16度 |

slotParams.action的值定义：

| 值       | 说明     | 例句                     |
| -------- | -------- | ------------------------ |
| increase | 温度增加 | 把客厅的空调温度调高一点 |
| reduce   | 温度减少 | 把客厅的空调温度调低一点 |
| set      | 温度设置 | 把客厅的空调调到24度     |



### fanSet —— 风扇设置

例句：把客厅的风扇调成摇头模式

```json
{
    "matchIntent": "fanSet",
    "slotParams": {
        "action": "shakeHead",
        "device": "风扇",
        "position": "客厅"
    }
}
```

| 参数                | 类型   | 说明     | 备注       |
| ------------------- | ------ | -------- | ---------- |
| slotParams.action   | string | 功能     | 见下文描述 |
| slotParams.device   | string | 设备名称 | 可省略     |
| slotParams.position | string | 位置名称 | 可省略     |

slotParams.action的值定义：

| 值          | 说明   | 例句                   |
| ----------- | ------ | ---------------------- |
| foreward    | 正向吹 | 把客厅的风扇设成正转   |
| reverse     | 反向吹 | 把客厅的风扇设成反转   |
| shakeHead   | 摇头吹 | 把客厅的风扇设成摇头   |
| sleepWind   | 睡眠风 | 把客厅的风扇设成睡眠风 |
| naturalWind | 自然风 | 把客厅的风扇设成自然风 |
| normalWind  | 正常风 | 把客厅的风扇设成正常风 |



### windSpeedSet —— 风速设置

例句：把客厅的风扇调到6挡

```json
{
    "matchIntent": "windSpeedSet",
    "slotParams": {
        "action": "set",
        "device": "风扇",
        "position": "客厅",
        "value": 6
    }
}
```

| 参数                | 类型   | 说明     | 备注                                                         |
| ------------------- | ------ | -------- | ------------------------------------------------------------ |
| slotParams.action   | string | 功能     | 见下文描述                                                   |
| slotParams.device   | string | 设备名称 | 可省略                                                       |
| slotParams.position | string | 位置名称 | 可省略                                                       |
| slotParams.value    | int    | 风速挡位 | 范围1-6，action为increase或reduce时可省略<br />最大挡表示6挡，最小挡表示1挡 |

slotParams.action的值定义：

| 值       | 说明     | 例句                     |
| -------- | -------- | ------------------------ |
| increase | 风速增加 | 把客厅的风扇风速调大一点 |
| reduce   | 风速减少 | 把客厅的风扇风速调小一些 |
| set      | 风速设置 | 把客厅的风扇调到6挡      |



### volumeSet —— 音量设置

例句：把客厅的电视音量调到60%

```json
{
    "matchIntent": "volumeSet",
    "slotParams": {
        "action": "set",
        "device": "电视",
        "position": "客厅",
        "value": 60
    }
}
```

| 参数                | 类型   | 说明     | 备注                                     |
| ------------------- | ------ | -------- | ---------------------------------------- |
| slotParams.action   | string | 功能     | 见下文描述                               |
| slotParams.device   | string | 设备名称 | 可省略                                   |
| slotParams.position | string | 位置名称 | 可省略                                   |
| slotParams.value    | int    | 音量值   | 百分比，action为increase或reduce时可省略 |

slotParams.action的值定义：

| 值       | 说明     | 例句                         |
| -------- | -------- | ---------------------------- |
| increase | 音量增加 | 帮我把客厅的电视音量调高一点 |
| reduce   | 音量减少 | 帮我把客厅的电视音量调低一点 |
| set      | 音量设置 | 把客厅的电视音量调到60%      |



### call —— 呼叫服务

例句：帮我呼叫管理中心

```json
{
    "matchIntent": "call",
    "slotParams": {
        "user": "管理中心"
    }
}
```

| 参数            | 类型   | 说明     | 备注 |
| --------------- | ------ | -------- | ---- |
| slotParams.user | string | 用户名称 |      |



### exit —— 退出会话

例句：有事再叫你

```json
{
    "matchIntent": "exit",
}
```